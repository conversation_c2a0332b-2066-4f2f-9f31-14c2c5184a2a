import csv
import urllib.parse
from itertools import groupby

from cachalot.api import cachalot_disabled
from django.http import HttpResponse

from commission_settlement_platform.core.utils import current_timezone_now


def get_csv_filename(prefix):
    """
    生成CSV文件名

    Args:
        prefix: 文件名前缀

    Returns:
        格式化的文件名字符串
    """
    # 移除文件名中不允许的字符
    prefix = prefix.replace('/', '_').replace('\\', '_').replace(':', '_')
    timestamp = current_timezone_now().strftime('%Y%m%d%H%M%S')
    return f"{prefix}_{timestamp}.csv"


def format_number_for_csv(value):
    """
    格式化数字以防止Excel等工具将长数字显示为科学计数法
    """
    return f"{value}\t"


class OrderAggregatedDataCSVExporter:
    def __init__(
        self,
        order_filter_condition_kwargs=None,
        group_by_fields=None,
    ):
        self.order_filter_condition_kwargs = order_filter_condition_kwargs
        self.group_by_fields = group_by_fields
        self.filename = self.get_file_name()
        self.response = HttpResponse(content_type='text/csv; charset=utf-8-sig')
        encoded_filename = urllib.parse.quote(self.filename)
        self.response['Content-Disposition'] = (
            f'attachment; '
            f'filename="{encoded_filename}"; '
            f'filename*=UTF-8\'\'{encoded_filename}'
        )
        # 创建CSV写入器
        self.writer = csv.writer(self.response)

    @property
    def order_aggregated_data(self):
        from core.admin.querysets import get_order_aggregated_data
        return get_order_aggregated_data(
            order_filter_condition_kwargs=self.order_filter_condition_kwargs,
            group_by_fields=self.group_by_fields
        )

    def get_file_name(self):
        return get_csv_filename(f"未命名")

    def get_csv_response(self, *args, **kwargs):
        with cachalot_disabled():  # 表格导出禁用缓存
            self.write_content()
            return self.response

    def write_content(self, *args, **kwargs):
        # 写入CSV头
        headers = [
            '#',
            '营销团队',
            '商品ID',
            '商品名称',
            '商品规格',
            '营销账号昵称',
            '营销账号ID',
            '订单提交时间',
            '商品单价',
            '销售数量',
            '订单应付金额',
            '结算比例(小数)',
            '结算金额',
        ]
        self.writer.writerow(headers)

        # 转换order_data为列表以便计算总和
        order_data_list = list(self.order_aggregated_data.order_by(
            'marketing_account__marketing_team__team_name',
            'marketing_account__account_id',
            '-order_created_date',
            'product_id',
            'product_specification',
        ))

        # 计算合计
        total_deals_count = sum(row['deals_count'] for row in order_data_list)
        total_order_actual_payment = sum(row['order_actual_payment'] for row in order_data_list)
        total_settle_amount = sum(row['settle_amount'] for row in order_data_list)

        # 写入数据
        for index, row in enumerate(order_data_list, 1):
            self.writer.writerow([
                index,  # 序号
                row['marketing_account__marketing_team__team_name'],
                format_number_for_csv(row['product_id']),
                row['product_name'],
                row['product_specification'],
                row['marketing_account__account_name'],
                format_number_for_csv(row['marketing_account__account_id']),
                row['order_created_date'],
                row['product_unit_price'],
                row['deals_count'],
                row['order_actual_payment'],
                row['settle_rate_decimal'],
                row['settle_amount'],
            ])

        # 写入汇总行
        self.writer.writerow([
            "",  # 序号列显示"合计"
            "",  # 营销团队
            "",  # 商品ID
            "",  # 商品名称
            "",  # 商品规格
            "",  # 营销账号昵称
            "",  # 营销账号ID
            "",  # 订单提交时间
            "",  # 商品单价
            f'合计: {total_deals_count}',  # 总销售数量
            f'合计: {total_order_actual_payment}',  # 总订单应付金额
            "",  # 结算比例
            f'合计: {total_settle_amount}',  # 总结算金额
        ])


class OrderAggregatedDataOldSystemFormatCSVExporter(OrderAggregatedDataCSVExporter):
    def __init__(
        self,
        order_filter_condition_kwargs=None,
        group_by_fields=None,
    ):
        super().__init__(
            order_filter_condition_kwargs=order_filter_condition_kwargs,
            group_by_fields=group_by_fields or [
                'marketing_account__marketing_team__team_name',
                'marketing_account__marketing_team_id',  # 用来给 url 传值
                'marketing_account_id',  # 用来给 url 传值
                'marketing_account__account_id',
                'marketing_account__account_name',
                'product_id',
                'product_name',
                # 'product_specification',
                'order_created_date',
                # 'product_unit_price',
                'settle_rate_decimal'
            ]
        )

    def write_content(self, *args, **kwargs):
        # 写入CSV头
        headers = [
            '#',
            '营销团队',
            '商品ID',
            '商品名称',
            # '商品规格',
            '营销账号昵称',
            '营销账号ID',
            '订单提交时间',
            # '商品单价',
            '销售数量',
            '订单应付金额',
            '结算比例(小数)',
            '结算金额',
        ]
        self.writer.writerow(headers)

        # 转换order_data为列表以便计算总和
        order_data_list = list(self.order_aggregated_data.order_by(
            'marketing_account__marketing_team__team_name',
            'marketing_account__account_id',
            '-order_created_date',
            'product_id',
            # 'product_specification',
        ))

        # 计算合计
        total_deals_count = sum(row['deals_count'] for row in order_data_list)
        total_order_actual_payment = sum(row['order_actual_payment'] for row in order_data_list)
        total_settle_amount = sum(row['settle_amount'] for row in order_data_list)

        # 写入数据
        for index, row in enumerate(order_data_list, 1):
            self.writer.writerow([
                index,  # 序号
                row['marketing_account__marketing_team__team_name'],
                format_number_for_csv(row['product_id']),
                row['product_name'],
                # row['product_specification'],
                row['marketing_account__account_name'],
                format_number_for_csv(row['marketing_account__account_id']),
                row['order_created_date'],
                # row['product_unit_price'],
                row['deals_count'],
                row['order_actual_payment'],
                row['settle_rate_decimal'],
                row['settle_amount'],
            ])

        # 写入汇总行
        self.writer.writerow([
            "",  # 序号列显示"合计"
            "",  # 营销团队
            "",  # 商品ID
            "",  # 商品名称
            # "",  # 商品规格
            "",  # 营销账号昵称
            "",  # 营销账号ID
            "",  # 订单提交时间
            # "",  # 商品单价
            f'合计: {total_deals_count}',  # 总销售数量
            f'合计: {total_order_actual_payment}',  # 总订单应付金额
            "",  # 结算比例
            f'合计: {total_settle_amount}',  # 总结算金额
        ])


class MarketingAccountCSVExporter(OrderAggregatedDataCSVExporter):
    def __init__(self, marketing_account_id, full_settle_state):
        self.marketing_account_id = marketing_account_id
        self.full_settle_state = full_settle_state
        super().__init__(
            order_filter_condition_kwargs={
                'marketing_account_id': marketing_account_id,
                'full_settle_state': full_settle_state,
            }
        )

    def get_file_name(self):
        from commission_settlement_platform.core.models import MarketingAccount
        marketing_account = MarketingAccount.objects.get(id=self.marketing_account_id)
        return get_csv_filename(f"营销账号({marketing_account.account_id})_{self.full_settle_state}_订单汇总")


class MarketingTeamCSVExporter(OrderAggregatedDataCSVExporter):
    def __init__(self, marketing_team_id_list, full_settle_state):
        self.marketing_team_id_list = marketing_team_id_list
        self.full_settle_state = full_settle_state
        super().__init__(
            order_filter_condition_kwargs={
                'marketing_account__marketing_team_id__in': marketing_team_id_list,
                'full_settle_state': full_settle_state,
            }
        )

    def get_file_name(self):
        from commission_settlement_platform.core.models import MarketingTeam
        marketing_teams = MarketingTeam.objects.filter(id__in=self.marketing_team_id_list)
        full_settle_state_str = self.full_settle_state if self.full_settle_state else '所有结算状态'
        if marketing_teams.count() == 1:
            team_names = marketing_teams.first().team_name
        else:
            team_names = ' '.join(i.team_name for i in marketing_teams)
        return get_csv_filename(f"营销团队({team_names})_{full_settle_state_str}_订单汇总")


class SettleWorkOrderCSVExporter(OrderAggregatedDataCSVExporter):
    def __init__(self, settle_work_order_id, marketing_account__marketing_team_id):
        self.settle_work_order_id = settle_work_order_id
        super().__init__(
            order_filter_condition_kwargs={
                'settle_work_order_id': settle_work_order_id,
                'marketing_account__marketing_team_id': marketing_account__marketing_team_id,
            }
        )

    def get_file_name(self):
        team_name_set = set(data['marketing_account__marketing_team__team_name'] for data in self.order_aggregated_data)
        if len(team_name_set) != 1:
            return get_csv_filename(f"结算工单({self.settle_work_order_id})_订单汇总")  # 如果有多个营销团队，则显示结算工单 id
        else:
            return get_csv_filename(f"{team_name_set.pop()}_结算订单汇总")  # 如果只有一个营销团队，则显示营销团队名称

    def write_content(self):
        # 写入CSV头
        headers = [
            '#',
            '营销团队',
            '商品ID',
            '商品名称',
            '商品规格',
            '营销账号昵称',
            '营销账号ID',
            '订单提交时间',
            '商品单价',
            '销售数量',
            '订单应付金额',
            '结算比例(小数)',
            '结算金额',
        ]

        order_data_groupby_marketing_team = groupby(
            self.order_aggregated_data.order_by(
                'marketing_account__marketing_team__team_name',
                'marketing_account__account_id',
                '-order_created_date',
                'product_id',
                'product_specification',
            ), key=lambda x: x['marketing_account__marketing_team__team_name']
        )

        for marketing_team_name, rows in order_data_groupby_marketing_team:
            self.writer.writerow(headers)

            # 转换rows为列表以便多次遍历
            rows_list = list(rows)

            # 计算汇总数据
            total_deals_count = sum(row['deals_count'] for row in rows_list)
            total_order_actual_payment = sum(row['order_actual_payment'] for row in rows_list)
            total_settle_amount = sum(row['settle_amount'] for row in rows_list)

            # 写入每一行数据
            for index, row in enumerate(rows_list, 1):
                self.writer.writerow([
                    index,  # 序号
                    row['marketing_account__marketing_team__team_name'],
                    format_number_for_csv(row['product_id']),
                    row['product_name'],
                    row['product_specification'],
                    row['marketing_account__account_name'],
                    format_number_for_csv(row['marketing_account__account_id']),
                    row['order_created_date'],
                    row['product_unit_price'],
                    row['deals_count'],
                    row['order_actual_payment'],
                    row['settle_rate_decimal'],
                    row['settle_amount'],
                ])

            # 写入汇总行
            self.writer.writerow([
                "",  # 序号列显示"合计"
                "",  # 团队名称
                "",  # 商品ID
                "",  # 商品名称
                "",  # 商品规格
                "",  # 营销账号昵称
                "",  # 营销账号ID
                "",  # 订单提交时间
                "",  # 商品单价
                f'合计: {total_deals_count}',  # 总销售数量
                f'合计: {total_order_actual_payment}',  # 总订单应付金额
                "",  # 结算比例
                f'合计: {total_settle_amount}',  # 总结算金额
            ])

            for _ in range(5):
                self.writer.writerow([])


class MarketingAccountOldSystemFormatCSVExporter(OrderAggregatedDataOldSystemFormatCSVExporter):
    def __init__(self, marketing_account_id, full_settle_state):
        self.marketing_account_id = marketing_account_id
        self.full_settle_state = full_settle_state
        super().__init__(
            order_filter_condition_kwargs={
                'marketing_account_id': marketing_account_id,
                'full_settle_state': full_settle_state,
            }
        )

    def get_file_name(self):
        from commission_settlement_platform.core.models import MarketingAccount
        marketing_account = MarketingAccount.objects.get(id=self.marketing_account_id)
        return get_csv_filename(
            f"营销账号({marketing_account.account_id})_{self.full_settle_state}_订单汇总_旧系统格式")


class MarketingTeamOldSystemFormatCSVExporter(OrderAggregatedDataOldSystemFormatCSVExporter):
    def __init__(self, marketing_team_id, full_settle_state):
        self.marketing_team_id = marketing_team_id
        self.full_settle_state = full_settle_state
        super().__init__(
            order_filter_condition_kwargs={
                'marketing_account__marketing_team_id': marketing_team_id,
                'full_settle_state': full_settle_state,
            }
        )

    def get_file_name(self):
        from commission_settlement_platform.core.models import MarketingTeam
        marketing_team = MarketingTeam.objects.get(id=self.marketing_team_id)
        full_settle_state_str = self.full_settle_state if self.full_settle_state else '所有结算状态'
        return get_csv_filename(f"营销团队({marketing_team.team_name})_{full_settle_state_str}_订单汇总_旧系统格式")


class SettleWorkOrderOldSystemFormatCSVExporter(OrderAggregatedDataOldSystemFormatCSVExporter):
    def __init__(self, settle_work_order_id, marketing_account__marketing_team_id):
        self.settle_work_order_id = settle_work_order_id
        super().__init__(
            order_filter_condition_kwargs={
                'settle_work_order_id': settle_work_order_id,
                'marketing_account__marketing_team_id': marketing_account__marketing_team_id,
            }
        )

    def get_file_name(self):
        team_name_set = set(data['marketing_account__marketing_team__team_name'] for data in self.order_aggregated_data)
        if len(team_name_set) != 1:
            return get_csv_filename(
                f"结算工单({self.settle_work_order_id})_订单汇总_旧系统格式")  # 如果有多个营销团队，则显示结算工单 id
        else:
            return get_csv_filename(f"{team_name_set.pop()}_结算订单汇总_旧系统格式")  # 如果只有一个营销团队，则显示营销团队名称

    def write_content(self):
        # 写入CSV头
        headers = [
            '#',
            '营销团队',
            '商品ID',
            '商品名称',
            # '商品规格',  # 旧系统不包含商品规格
            '营销账号昵称',
            '营销账号ID',
            '订单提交时间',
            # '商品单价',  # 旧系统不按单价分组，不显示单价
            '销售数量',
            '订单应付金额',
            '结算比例(小数)',
            '结算金额',
        ]

        order_data_groupby_marketing_team = groupby(
            self.order_aggregated_data.order_by(
                'marketing_account__marketing_team__team_name',
                'marketing_account__account_id',
                '-order_created_date',
                'product_id',
                # 'product_specification',  # 旧系统不按商品规格排序
            ), key=lambda x: x['marketing_account__marketing_team__team_name']
        )

        for marketing_team_name, rows in order_data_groupby_marketing_team:
            self.writer.writerow(headers)

            # 转换rows为列表以便多次遍历
            rows_list = list(rows)

            # 计算汇总数据
            total_deals_count = sum(row['deals_count'] for row in rows_list)
            total_order_actual_payment = sum(row['order_actual_payment'] for row in rows_list)
            total_settle_amount = sum(row['settle_amount'] for row in rows_list)

            # 写入每一行数据
            for index, row in enumerate(rows_list, 1):
                self.writer.writerow([
                    index,  # 序号
                    row['marketing_account__marketing_team__team_name'],
                    format_number_for_csv(row['product_id']),
                    row['product_name'],
                    # row['product_specification'],  # 旧系统不包含商品规格
                    row['marketing_account__account_name'],
                    format_number_for_csv(row['marketing_account__account_id']),
                    row['order_created_date'],
                    # row['product_unit_price'],  # 旧系统不显示单价
                    row['deals_count'],
                    row['order_actual_payment'],
                    row['settle_rate_decimal'],
                    row['settle_amount'],
                ])

            # 写入汇总行
            self.writer.writerow([
                "",  # 序号列显示"合计"
                "",  # 团队名称
                "",  # 商品ID
                "",  # 商品名称
                # "",  # 商品规格
                "",  # 营销账号昵称
                "",  # 营销账号ID
                "",  # 订单提交时间
                # "",  # 商品单价
                f'合计: {total_deals_count}',  # 总销售数量
                f'合计: {total_order_actual_payment}',  # 总订单应付金额
                "",  # 结算比例
                f'合计: {total_settle_amount}',  # 总结算金额
            ])

            for _ in range(5):
                self.writer.writerow([])
