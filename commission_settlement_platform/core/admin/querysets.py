from django.db.models import Q, F, Sum
from django.db.models.functions import TruncDate, Coalesce
from commission_settlement_platform.core.utils import compact_all_falsy_dict


def get_order_aggregated_data(
    order_filter_condition_kwargs=None,
    group_by_fields=None,
):
    from commission_settlement_platform.core.models import Order

    if order_filter_condition_kwargs is None:
        order_filter_condition_kwargs = {}

    if group_by_fields is None:
        group_by_fields = [
            'marketing_account__marketing_team__team_name',
            'marketing_account__marketing_team_id',  # 用来给 url 传值
            'marketing_account_id',  # 用来给 url 传值
            'marketing_account__account_id',
            'marketing_account__account_name',
            'product_id',
            'product_name',
            'product_specification',
            'order_created_date',
            'product_unit_price',
            'settle_rate_decimal'
        ]

    # 构建过滤条件
    filter_condition = Q()
    filter_condition &= Q(
        **compact_all_falsy_dict(order_filter_condition_kwargs)
    )

    # 根据条件 groupby 聚合
    order_aggregated_data = Order.objects.filter(filter_condition).annotate(
        order_created_date=TruncDate(F('order_created_dt')),
        marketing_account__marketing_team__team_name=Coalesce(
            F('marketing_account__marketing_team__team_name'),
            F('approved_marketing_team_name')
        )
    ).values(
        *group_by_fields
    ).annotate(
        deals_count=Sum('deals_count'),
        order_actual_payment=Sum('order_actual_payment'),
        settle_amount=Sum('settle_amount'),
    )

    return order_aggregated_data


def get_marketing_account_order_data(marketing_account_id, full_settle_state=None):
    """
    获取营销账号的订单数据

    Args:
        marketing_account_id: 营销账号ID
        full_settle_state: 结算状态过滤条件

    Returns:
        订单数据查询集
    """
    order_aggregated_data = get_order_aggregated_data(
        order_filter_condition_kwargs={
            'marketing_account_id': marketing_account_id,
            'full_settle_state': full_settle_state,
        },
        group_by_fields=[
            'product_id',
            'product_name',
            'product_specification',
            'order_created_date',
            'product_unit_price',
            'settle_rate_decimal'
        ]
    ).order_by(
        '-order_created_date',
        'product_id',
        'product_specification',
    )

    return order_aggregated_data


def get_marketing_team_order_data(marketing_team_id, full_settle_state=None):
    """
    获取营销团队的订单数据

    Args:
        marketing_team_id: 营销团队ID
        full_settle_state: 结算状态过滤条件

    Returns:
        订单数据查询集
    """
    order_aggregated_data = get_order_aggregated_data(
        order_filter_condition_kwargs={
            'marketing_account__marketing_team_id': marketing_team_id,
            'full_settle_state': full_settle_state,
        },
        group_by_fields=[
            'marketing_account_id',
            'marketing_account__account_id',
            'marketing_account__account_name',
            'product_id',
            'product_name',
            'product_specification',
            'order_created_date',
            'product_unit_price',
            'settle_rate_decimal'
        ]
    ).order_by(
        'marketing_account__account_id',
        '-order_created_date',
        'product_id',
        'product_specification',
    )

    return order_aggregated_data


def get_settle_work_order_data(settle_work_order_id, marketing_team_id=None):
    """
    获取结算工单的订单数据

    Args:
        settle_work_order_id: 结算工单ID
        marketing_team_id: 可选的营销团队ID过滤条件

    Returns:
        订单数据查询集
    """
    # 根据条件 groupby 聚合
    order_aggregated_data = get_order_aggregated_data(
        order_filter_condition_kwargs={
            'settle_work_order_id': settle_work_order_id,
            'marketing_account__marketing_team_id': marketing_team_id,
        },
        group_by_fields=[
            'marketing_account__marketing_team__team_name',
            'marketing_account__marketing_team_id',  # 用来给 url 传值
            'marketing_account_id',  # 用来给 url 传值
            'marketing_account__account_id',
            'marketing_account__account_name',
            'product_id',
            'product_name',
            'product_specification',
            'order_created_date',
            'product_unit_price',
            'settle_rate_decimal'
        ]
    ).order_by(
        'marketing_account__marketing_team__team_name',
        'marketing_account__account_id',
        '-order_created_date',
        'product_id',
        'product_specification',
    )

    return order_aggregated_data
