from django.contrib import admin
from django.contrib import messages
from django.core.paginator import Paginator, EmptyPage
from django.db import transaction
from django.shortcuts import redirect, render
from django.urls import reverse, path
from django.utils import timezone
from django.utils.safestring import SafeString

from commission_settlement_platform.core.admin.display_functions import (
    unsettle_amount,
    settling_amount,
    settled_amount,
    can_not_settle_amount,
    abnormal_settle_amount,
    abnormal_settle_amount_rate, approved_settle_amount
)
from commission_settlement_platform.core.admin.export_statistics_csv_tools import (
    MarketingTeamCSVExporter, MarketingTeamOldSystemFormatCSVExporter
)
from commission_settlement_platform.core.admin.list_filters import MarketingTeamOrderFullSettleStateFilter, \
    DateRangeWithQuerySetHookFilterBuilder
from commission_settlement_platform.core.admin.querysets import get_marketing_team_order_data
from commission_settlement_platform.core.models import MarketingTeam, Order, SettleWorkOrder


@admin.register(MarketingTeam)
class MarketingTeamAdmin(admin.ModelAdmin):
    @admin.display(
        description='订单统计',
        ordering='order_created_date'
    )
    def order_statistics(self, obj):
        view = self.order_statistics_view(self.request, obj.id)
        return SafeString(view.content.decode('utf-8'))

    def get_form(self, request, obj=None, **kwargs):
        # 这里需要传入 request，否则在 order_statistics_view 中无法获取 request
        self.request = request
        return super().get_form(request, obj, **kwargs)

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('<path:object_id>/order-statistics/',
                 self.admin_site.admin_view(self.order_statistics_view),
                 name='marketing_team_order_statistics'),
            path('<path:object_id>/export-csv/',
                 self.admin_site.admin_view(self.export_order_statistics_csv),
                 name='marketing_team_export_csv'),
            path('<path:object_id>/export-old-system-format-csv/',
                 self.admin_site.admin_view(self.export_old_system_format_order_statistics_csv),
                 name='marketing_team_export_old_system_format_csv'),
            path('/bulk-export-csv/',
                 self.admin_site.admin_view(self.bulk_export_order_statistics_csv_view),
                 name='bulk_export_order_statistics_csv'),

        ]
        return custom_urls + urls

    def export_order_statistics_csv(self, request, object_id):
        """导出订单摘要为CSV文件"""
        if object_id:
            # 从请求中获取结算状态过滤条件（如果有）
            full_settle_state = request.GET.get('full_settle_state', '')

            # 使用新的CSV导出器
            csv_exporter = MarketingTeamCSVExporter(
                marketing_team_id_list=[object_id],
                full_settle_state=full_settle_state,
            )
            return csv_exporter.get_csv_response()
        return None

    def export_old_system_format_order_statistics_csv(self, request, object_id):
        """导出订单摘要为CSV文件（旧系统格式）"""
        if object_id:
            # 从请求中获取结算状态过滤条件（如果有）
            full_settle_state = request.GET.get('full_settle_state', '')

            # 使用旧系统格式的CSV导出器
            csv_exporter = MarketingTeamOldSystemFormatCSVExporter(
                marketing_team_id=object_id,
                full_settle_state=full_settle_state,
            )
            return csv_exporter.get_csv_response()
        return None

    def order_statistics_view(self, request, object_id):
        if object_id:
            # 从请求中获取结算状态过滤条件
            full_settle_state = request.GET.get('full_settle_state', '未结算')

            # 获取订单数据
            order_data = get_marketing_team_order_data(object_id, full_settle_state)

            # 计算总计数据
            from django.db.models import Sum
            statistics = order_data.aggregate(
                deals_count_sum=Sum('deals_count'),
                order_actual_payment_sum=Sum('order_actual_payment'),
                settle_amount_sum=Sum('settle_amount')
            )

            # 每页显示10条数据
            paginator = Paginator(order_data, 10)
            page = request.GET.get('page', 1)

            try:
                page_obj = paginator.page(page)
            except EmptyPage:
                page_obj = paginator.page(1)

            # 获取分页范围，只显示当前页相邻的2个页码
            page_range = paginator.get_elided_page_range(number=page_obj.number, on_each_side=2, on_ends=0)

            context = {
                'page_obj': page_obj,
                'paginator': paginator,
                'page_range': page_range,  # 使用自定义的分页范围
                'object_id': object_id,
                'full_settle_state': full_settle_state,
                'full_settle_states': list(Order.FULL_SETTLE_STATE_CHOICES.items()),
                'statistics': statistics,  # 添加总计值到context
                'export_csv_url': reverse('admin:marketing_team_export_csv', args=[object_id]),
                'export_old_system_format_csv_url': reverse(
                    'admin:marketing_team_export_old_system_format_csv',
                    args=[object_id]
                ),
            }

            if request.headers.get('HX-Request'):
                return render(
                    request,
                    'admin/marketingteam/order_statistics_table_content.html',
                    context
                )
            else:
                return render(
                    request,
                    'admin/marketingteam/order_statistics_table.html',
                    context
                )
        return '-'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        if request.resolver_match.view_name == 'admin:core_marketingteam_change':
            return MarketingTeam.contribute_order_statistics(queryset)
        return queryset

    @admin.action(description="创建结算工单")
    def create_settle_work_order(self, request, queryset):
        marketing_team_id_list = list(queryset.values_list('id', flat=True))

        # 检查是否有营销团队
        if not marketing_team_id_list:
            self.message_user(request, "未选择营销团队", level=messages.ERROR)
            return None

        try:
            with transaction.atomic():
                settle_work_order = SettleWorkOrder.objects.create(
                    selected_marketing_team_id_list=marketing_team_id_list
                )
                self.message_user(request, f"已创建结算工单，保存工单后将执行关联订单任务")
                return redirect(reverse('admin:core_settleworkorder_change', args=[settle_work_order.id]))
        except Exception as e:
            self.message_user(request, f"创建结算工单失败: {str(e)}", level=messages.ERROR)
            return None

    @admin.action(description="批量导出订单统计csv")
    def bulk_export_order_statistics_csv_action(self, request, queryset):
        """导出订单摘要为CSV文件"""
        full_settle_state = request.GET.get('full_settle_state', '未结算')
        marketing_team_ids = ','.join(str(i.id) for i in queryset)
        context = dict(
            self.admin_site.each_context(request),
            opts=self.opts,
            title='批量导出订单统计csv',
            app_label=self.model._meta.app_label,
            model_name=self.model._meta.model_name,
            verbose_name=self.model._meta.verbose_name,
            verbose_name_plural=self.model._meta.verbose_name_plural,
            changelist_url=reverse('admin:core_marketingteam_changelist'),
            marketing_teams=queryset,
            full_settle_states=list(Order.FULL_SETTLE_STATE_CHOICES.items()),
            marketing_team_ids=marketing_team_ids
        )
        return render(
            request,
            'admin/marketingteam/bulk_export_order_statistics_csv.html',
            context
        )

    def bulk_export_order_statistics_csv_view(self, request):
        # 从请求中获取结算状态过滤条件（如果有）
        marketing_team_ids = request.GET.get('marketing_team_ids')
        marketing_team_id_list = [int(i) for i in marketing_team_ids.split(',')]
        full_settle_state = request.GET.get('full_settle_state', '')

        # 使用新的CSV导出器
        csv_exporter = MarketingTeamCSVExporter(
            marketing_team_id_list=marketing_team_id_list,
            full_settle_state=full_settle_state,
        )
        return csv_exporter.get_csv_response()

    statistics_fields = [
        unsettle_amount,
        settling_amount,
        settled_amount,
        can_not_settle_amount,
        abnormal_settle_amount,
        approved_settle_amount,
        abnormal_settle_amount_rate,
    ]
    list_display = ['team_name'] + statistics_fields
    fields = ['team_name'] + statistics_fields + ['order_statistics']
    readonly_fields = statistics_fields + ['order_statistics']
    list_filter = [
        ['marketingaccount__order__order_created_dt', DateRangeWithQuerySetHookFilterBuilder(
            default_start=timezone.now(),
            default_end=timezone.now(),
            queryset_hook_func=MarketingTeam.contribute_order_statistics
        )],
        ['marketingaccount__order__full_settle_state', MarketingTeamOrderFullSettleStateFilter]
    ]
    search_fields = ['team_name']
    actions = ['create_settle_work_order', 'bulk_export_order_statistics_csv_action']
