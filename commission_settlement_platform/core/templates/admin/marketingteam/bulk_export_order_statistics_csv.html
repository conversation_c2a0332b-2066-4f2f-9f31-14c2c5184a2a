{% extends "admin/base_site.html" %}
{% load htmx %}
{% load i18n %}

{% block breadcrumbs %}
  <ul>
    <li><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li><a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst }}</a></li>
    <li><a href="{{ changelist_url }}">{{ verbose_name_plural|capfirst }}</a></li>
    {% if title %}
      <li>{{ title }}</li>
    {% endif %}
  </ul>
{% endblock %}

{% block content %}
  <div class="grp-group">
    <div class="grp-module">
      <h2>结算状态选择</h2>
      <div class="grp-row">
        <div>
          <div class="filter-section">
            <label for="full_settle_state">完整结算状态：
              <select name="full_settle_state"
                      hx-get="{% url 'admin:bulk_export_order_statistics_csv' %}"
                      hx-target="#export-buttons"
                      hx-include="[name='full_settle_state']"
                      hx-vals='{"marketing_team_ids": "{{ marketing_team_ids }}", "update_links": "true"}'>
                <option value="" {% if not full_settle_state %}selected{% endif %}>&mdash;&mdash;&mdash;</option>
                {% for value, label in full_settle_states %}
                  <option value="{{ value }}"
                          {% if value == full_settle_state %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
              </select>
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="grp-module">
      <h2>已选中的营销团队</h2>
      <ul>
        {% for marketing_team in marketing_teams %}
          <li class="grp-row">{{ marketing_team }}</li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <footer class="grp-module grp-submit-row grp-fixed-footer">
    <header style="display:none">
      <h1>Submit Options</h1>
    </header>
    <div id="export-buttons">
      <div>
        <a
          href="{% url 'admin:bulk_export_order_statistics_csv' %}?marketing_team_ids={{ marketing_team_ids }}&full_settle_state={{ full_settle_state }}"
          class="grp-button grp-default"
          title="导出全部数据为CSV">
          导出CSV
        </a>
      </div>
      <div>
        <a
          href="{% url 'admin:bulk_export_order_statistics_csv' %}?marketing_team_ids={{ marketing_team_ids }}&full_settle_state={{ full_settle_state }}&old_system=true"
          class="grp-button grp-default"
          title="导出全部数据为CSV（旧系统格式）">
          导出CSV（旧系统格式）
        </a>
      </div>
    </div>
  </footer>
{% endblock %}
