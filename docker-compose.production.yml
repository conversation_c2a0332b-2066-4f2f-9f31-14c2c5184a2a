volumes:
  production_postgres_data: {}
  production_postgres_data_backups: {}
  production_traefik: {}
  production_django_media: {}
  production_django_protected: {}
  production_redis_data: {}



services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile

    image: commission_settlement_platform_production_django
    cpu_shares: 512
    volumes:
      - production_django_media:/app/commission_settlement_platform/media
      - production_django_protected:/app/commission_settlement_platform/protected
    depends_on:
      - postgres
      - redis
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: commission_settlement_platform_production_postgres
    cpu_shares: 1024
    volumes:
      - production_postgres_data:/var/lib/postgresql/data
      - production_postgres_data_backups:/backups
    env_file:
      - ./.envs/.production/.postgres
    ports:
      - '0.0.0.0:5432:5432'

  traefik:
    build:
      context: .
      dockerfile: ./compose/production/traefik/Dockerfile
    image: commission_settlement_platform_production_traefik
    depends_on:
      - django
      - nginx
    volumes:
      - production_traefik:/etc/traefik/acme
    ports:
      - '0.0.0.0:1080:1080'
      - '0.0.0.0:1443:1443'
      - '0.0.0.0:1555:1555'

  redis:
    build:
      context: .
      dockerfile: ./compose/production/redis/Dockerfile
    cpu_shares: 1024
    volumes:
      - production_redis_data:/data
    command: redis-server /usr/local/etc/redis/redis.conf

  celeryworker:
    <<: *django
    image: commission_settlement_platform_production_celeryworker
    command: /start-celeryworker
#    cpu_period: 100000
#    cpu_quota: 25000
    cpu_shares: 64

  celerybeat:
    <<: *django
    image: commission_settlement_platform_production_celerybeat
    command: /start-celerybeat
    cpu_shares: 256

  flower:
    <<: *django
    image: commission_settlement_platform_production_flower
    command: /start-flower
    cpu_shares: 128

  nginx:
    build:
      context: .
      dockerfile: ./compose/production/nginx/Dockerfile
    image: commission_settlement_platform_production_nginx
    cpu_shares: 1024
    depends_on:
      - django
    volumes:
      - production_django_media:/usr/share/nginx/media:ro
      - production_django_protected:/usr/share/nginx/protected:ro
